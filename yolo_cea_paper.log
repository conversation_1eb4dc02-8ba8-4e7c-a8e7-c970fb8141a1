This is XeTeX, Version 3.141592653-2.6-0.999996 (TeX Live 2024) (preloaded format=xelatex 2024.9.4)  8 AUG 2025 11:43
entering extended mode
 restricted \write18 enabled.
 file:line:error style messages enabled.
 %&-line parsing enabled.
**yolo_cea_paper
(./yolo_cea_paper.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-02-20>
(./IEEEtran.cls
Document Class: IEEEtran 2015/08/26 V1.8b by <PERSON>
-- See the "IEEEtran_HOWTO" manual for usage information.
-- http://www.michaelshell.org/tex/ieeetran/
\@IEEEtrantmpdimenA=\dimen140
\@IEEEtrantmpdimenB=\dimen141
\@IEEEtrantmpdimenC=\dimen142
\@IEEEtrantmpcountA=\count184
\@IEEEtrantmpcountB=\count185
\@IEEEtrantmpcountC=\count186
\@IEEEtrantmptoksA=\toks17
LaTeX Font Info:    Trying to load font information for TU+ptm on input line 503.
LaTeX Font Info:    No file TUptm.fd. on input line 503.

LaTeX Font Warning: Font shape `TU/ptm/m/n' undefined
(Font)              using `TU/lmr/m/n' instead on input line 503.

-- Using 8.5in x 11in (letter) paper.
-- Using DVI output.
\@IEEEnormalsizeunitybaselineskip=\dimen143
-- This is a 10 point document.
\CLASSINFOnormalsizebaselineskip=\dimen144
\CLASSINFOnormalsizeunitybaselineskip=\dimen145
\IEEEnormaljot=\dimen146

LaTeX Font Warning: Font shape `TU/ptm/bx/n' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1086.


LaTeX Font Warning: Font shape `TU/ptm/m/it' undefined
(Font)              using `TU/ptm/m/n' instead on input line 1086.


LaTeX Font Warning: Font shape `TU/ptm/bx/it' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 1086.

\IEEEquantizedlength=\dimen147
\IEEEquantizedlengthdiff=\dimen148
\IEEEquantizedtextheightdiff=\dimen149
\IEEEilabelindentA=\dimen150
\IEEEilabelindentB=\dimen151
\IEEEilabelindent=\dimen152
\IEEEelabelindent=\dimen153
\IEEEdlabelindent=\dimen154
\IEEElabelindent=\dimen155
\IEEEiednormlabelsep=\dimen156
\IEEEiedmathlabelsep=\dimen157
\IEEEiedtopsep=\skip48
\c@section=\count187
\c@subsection=\count188
\c@subsubsection=\count189
\c@paragraph=\count190
\c@IEEEsubequation=\count191
\abovecaptionskip=\skip49
\belowcaptionskip=\skip50
\c@figure=\count192
\c@table=\count193
\@IEEEeqnnumcols=\count194
\@IEEEeqncolcnt=\count195
\@IEEEsubeqnnumrollback=\count196
\@IEEEquantizeheightA=\dimen158
\@IEEEquantizeheightB=\dimen159
\@IEEEquantizeheightC=\dimen160
\@IEEEquantizeprevdepth=\dimen161
\@IEEEquantizemultiple=\count197
\@IEEEquantizeboxA=\box51
\@IEEEtmpitemindent=\dimen162
\IEEEPARstartletwidth=\dimen163
\c@IEEEbiography=\count198
\@IEEEtranrubishbin=\box52
) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip51

For additional information on amsmath, use the `?' option.
(d:/texlive/2024/texmf-dist/tex/latex/amsmath/amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text
 (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks18
\ex@=\dimen164
)) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen165
) (d:/texlive/2024/texmf-dist/tex/latex/amsmath/amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count199
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count266
\leftroot@=\count267
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count268
\DOTSCASE@=\count269
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box53
\strutbox@=\box54
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen166
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count270
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count271
\dotsspace@=\muskip16
\c@parentequation=\count272
\dspbrk@lvl=\count273
\tag@help=\toks19
\row@=\count274
\column@=\count275
\maxfields@=\count276
\andhelp@=\toks20
\eqnshift@=\dimen167
\alignsep@=\dimen168
\tagshift@=\dimen169
\tagwidth@=\dimen170
\totwidth@=\dimen171
\lineht@=\dimen172
\@envbody=\toks21
\multlinegap=\skip52
\multlinetaggap=\skip53
\mathdisplay@stack=\toks22
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
) (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
) (d:/texlive/2024/texmf-dist/tex/latex/newtx/newtxmath.sty
Package: newtxmath 2024/03/06 v1.742
 `newtxmath' v1.742, 2024/03/06 Math macros based originally on txfonts (msharpe) (d:/texlive/2024/texmf-dist/tex/latex/base/ifthen.sty
Package: ifthen 2022/04/13 v1.1d Standard LaTeX ifthen package (DPC)
) (d:/texlive/2024/texmf-dist/tex/latex/etoolbox/etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count277
) (d:/texlive/2024/texmf-dist/tex/generic/iftex/iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
) (d:/texlive/2024/texmf-dist/tex/latex/xkeyval/xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)
 (d:/texlive/2024/texmf-dist/tex/generic/xkeyval/xkeyval.tex (d:/texlive/2024/texmf-dist/tex/generic/xkeyval/xkvutils.tex
\XKV@toks=\toks23
\XKV@tempa@toks=\toks24
 (d:/texlive/2024/texmf-dist/tex/generic/xkeyval/keyval.tex))
\XKV@depth=\count278
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
)) (d:/texlive/2024/texmf-dist/tex/latex/oberdiek/centernot.sty
Package: centernot 2016/05/16 v1.4 Centers the not symbol horizontally (HO)
)
\tx@cntz=\count279
 (d:/texlive/2024/texmf-dist/tex/generic/kastrup/binhex.tex)
\tx@Isdigit=\count280
\tx@IsAlNum=\count281
\tx@tA=\toks25
\tx@tB=\toks26
\tx@su=\read2

amsthm NOT loaded
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 393.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/minntx/m/n on input line 393.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/m/n on input line 393.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/minntx/m/n --> OT1/minntx/b/n on input line 394.
LaTeX Font Info:    Redeclaring math alphabet \mathsf on input line 401.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> OT1/phv/m/n on input line 401.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> OT1/phv/m/n on input line 401.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/phv/m/n --> OT1/phv/b/n on input line 403.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> OT1/minntx/m/it on input line 410.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> OT1/minntx/m/it on input line 410.
LaTeX Font Info:    Redeclaring math alphabet \mathtt on input line 411.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/pcr/m/n on input line 411.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/pcr/m/n on input line 411.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 413.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/b/n on input line 413.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/minntx/b/n on input line 413.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/minntx/m/it --> OT1/minntx/b/it on input line 414.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  TU/pcr/m/n --> TU/pcr/b/n on input line 417.
LaTeX Font Info:    Redeclaring symbol font `letters' on input line 519.
LaTeX Font Info:    Overwriting symbol font `letters' in version `normal'
(Font)                  OML/cmm/m/it --> OML/ntxmi/m/it on input line 519.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/cmm/b/it --> OML/ntxmi/m/it on input line 519.
LaTeX Font Info:    Overwriting symbol font `letters' in version `bold'
(Font)                  OML/ntxmi/m/it --> OML/ntxmi/b/it on input line 520.
\symlettersA=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `lettersA' in version `bold'
(Font)                  U/ntxmia/m/it --> U/ntxmia/b/it on input line 565.
LaTeX Font Info:    Redeclaring math alphabet \mathfrak on input line 567.
LaTeX Font Info:    Redeclaring symbol font `symbols' on input line 587.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `normal' on input line 587.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `normal'
(Font)                  OMS/cmsy/m/n --> LMS/ntxsy/m/n on input line 587.
LaTeX Font Info:    Encoding `OMS' has changed to `LMS' for symbol font
(Font)              `symbols' in the math version `bold' on input line 587.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  OMS/cmsy/b/n --> LMS/ntxsy/m/n on input line 587.
LaTeX Font Info:    Overwriting symbol font `symbols' in version `bold'
(Font)                  LMS/ntxsy/m/n --> LMS/ntxsy/b/n on input line 588.
\symAMSm=\mathgroup7
LaTeX Font Info:    Overwriting symbol font `AMSm' in version `bold'
(Font)                  U/ntxsym/m/n --> U/ntxsym/b/n on input line 613.
\symsymbolsC=\mathgroup8
LaTeX Font Info:    Overwriting symbol font `symbolsC' in version `bold'
(Font)                  U/ntxsyc/m/n --> U/ntxsyc/b/n on input line 634.
LaTeX Font Info:    Redeclaring symbol font `largesymbols' on input line 647.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `normal' on input line 647.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `normal'
(Font)                  OMX/cmex/m/n --> LMX/ntxexx/m/n on input line 647.
LaTeX Font Info:    Encoding `OMX' has changed to `LMX' for symbol font
(Font)              `largesymbols' in the math version `bold' on input line 647.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  OMX/cmex/m/n --> LMX/ntxexx/m/n on input line 647.
LaTeX Font Info:    Overwriting symbol font `largesymbols' in version `bold'
(Font)                  LMX/ntxexx/m/n --> LMX/ntxexx/b/n on input line 648.
\symlargesymbolsTXA=\mathgroup9
LaTeX Font Info:    Overwriting symbol font `largesymbolsTXA' in version `bold'
(Font)                  U/ntxexa/m/n --> U/ntxexa/b/n on input line 662.
\tx@sbptoks=\toks27
LaTeX Font Info:    Redeclaring math delimiter \lfloor on input line 885.
LaTeX Font Info:    Redeclaring math delimiter \rfloor on input line 886.
LaTeX Font Info:    Redeclaring math delimiter \lceil on input line 887.
LaTeX Font Info:    Redeclaring math delimiter \rceil on input line 888.
LaTeX Font Info:    Redeclaring math delimiter \lbrace on input line 893.
LaTeX Font Info:    Redeclaring math delimiter \rbrace on input line 894.
LaTeX Font Info:    Redeclaring math delimiter \langle on input line 896.
LaTeX Font Info:    Redeclaring math delimiter \rangle on input line 898.
LaTeX Font Info:    Redeclaring math delimiter \arrowvert on input line 902.
LaTeX Font Info:    Redeclaring math delimiter \vert on input line 903.
LaTeX Font Info:    Redeclaring math accent \dot on input line 974.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 975.
LaTeX Font Info:    Redeclaring math accent \vec on input line 2048.
LaTeX Info: Redefining \Bbbk on input line 2838.
LaTeX Info: Redefining \not on input line 2986.
) (d:/texlive/2024/texmf-dist/tex/latex/algorithms/algorithmic.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithmic 2009/08/24 v0.1 Document Style `algorithmic'
\c@ALC@unique=\count282
\c@ALC@line=\count283
\c@ALC@rem=\count284
\c@ALC@depth=\count285
\ALC@tlm=\skip54
\algorithmicindent=\skip55
) (d:/texlive/2024/texmf-dist/tex/latex/algorithms/algorithm.sty
Invalid UTF-8 byte or sequence at line 11 replaced by U+FFFD.
Package: algorithm 2009/08/24 v0.1 Document Style `algorithm' - floating environment
 (d:/texlive/2024/texmf-dist/tex/latex/float/float.sty
Package: float 2001/11/08 v1.3d Float enhancements (AL)
\c@float@type=\count286
\float@exts=\toks28
\float@box=\box55
\@float@everytoks=\toks29
\@floatcapt=\box56
)
\@float@every@algorithm=\toks30
\c@algorithm=\count287
) (d:/texlive/2024/texmf-dist/tex/latex/tools/array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen173
\ar@mcellbox=\box57
\extrarowheight=\dimen174
\NC@list=\toks31
\extratabsurround=\skip56
\backup@length=\skip57
\ar@cellbox=\box58
) (d:/texlive/2024/texmf-dist/tex/latex/booktabs/booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen175
\lightrulewidth=\dimen176
\cmidrulewidth=\dimen177
\belowrulesep=\dimen178
\belowbottomsep=\dimen179
\aboverulesep=\dimen180
\abovetopsep=\dimen181
\cmidrulesep=\dimen182
\cmidrulekern=\dimen183
\defaultaddspace=\dimen184
\@cmidla=\count288
\@cmidlb=\count289
\@aboverulesep=\dimen185
\@belowrulesep=\dimen186
\@thisruleclass=\count290
\@lastruleclass=\count291
\@thisrulewidth=\dimen187
) (d:/texlive/2024/texmf-dist/tex/latex/multirow/multirow.sty
Package: multirow 2021/03/15 v2.8 Span multiple rows of a table
\multirow@colwidth=\skip58
\multirow@cntb=\count292
\multirow@dima=\skip59
\bigstrutjot=\dimen188
)
./yolo_cea_paper.tex:10: Undefined control sequence.
<recently read> \pdfcompresslevel 
                                  
l.10 \pdfcompresslevel
                      =9 % 压缩级别，0-9，9为最高
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


./yolo_cea_paper.tex:10: LaTeX Error: Missing \begin{document}.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.10 \pdfcompresslevel=
                       9 % 压缩级别，0-9，9为最高
You're in trouble here.  Try typing  <return>  to proceed.
If that doesn't work, type  X <return>  to quit.

./yolo_cea_paper.tex:11: Undefined control sequence.
l.11 \pdfobjcompresslevel
                         =2 % 对象流压缩级别, 0-3, 2或3效果好
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

(d:/texlive/2024/texmf-dist/tex/latex/graphics/graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)
 (d:/texlive/2024/texmf-dist/tex/latex/graphics/trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
) (d:/texlive/2024/texmf-dist/tex/latex/graphics-cfg/graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 107.
 (d:/texlive/2024/texmf-dist/tex/latex/graphics-def/xetex.def
File: xetex.def 2022/09/22 v5.0n Graphics/color driver for xetex
))
\Gin@req@height=\dimen189
\Gin@req@width=\dimen190
) (d:/texlive/2024/texmf-dist/tex/latex/subfig/subfig.sty
Package: subfig 2005/06/28 ver: 1.3 subfig package
 (d:/texlive/2024/texmf-dist/tex/latex/caption/caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen191
\captionmargin=\dimen192
\caption@leftmargin=\dimen193
\caption@rightmargin=\dimen194
\caption@width=\dimen195
\caption@indent=\dimen196
\caption@parindent=\dimen197
\caption@hangindent=\dimen198
Package caption Info: Unknown document class (or package),
(caption)             standard defaults will be used.
Package caption Info: \@makecaption = \long macro:#1#2->\ifx \@captype \@IEEEtablestring \footnotesize \bgroup \par \centering \@IEEEtabletopskipstrut {\normalfont \footnotesize #1}\\{\normalfont \footnotesize \scshape #2}\par \addvspace {0.5\baselineskip }\egroup \@IEEEtablecaptionsepspace \else \@IEEEfigurecaptionsepspace \setbox \@tempboxa \hbox {\normalfont \footnotesize {#1.}\nobreakspace \nobreakspace #2}\ifdim \wd \@tempboxa >\hsize \setbox \@tempboxa \hbox {\normalfont \footnotesize {#1.}\nobreakspace \nobreakspace }\parbox [t]{\hsize }{\normalfont \footnotesize \noindent \unhbox \@tempboxa #2}\else \ifCLASSOPTIONconference \hbox to\hsize {\normalfont \footnotesize \hfil \box \@tempboxa \hfil }\else \hbox to\hsize {\normalfont \footnotesize \box \@tempboxa \hfil }\fi \fi \fi  on input line 1175.
)
\c@KVtest=\count293
\sf@farskip=\skip60
\sf@captopadj=\dimen199
\sf@capskip=\skip61
\sf@nearskip=\skip62
\c@subfigure=\count294
\c@subfigure@save=\count295
\c@lofdepth=\count296
\c@subtable=\count297
\c@subtable@save=\count298
\c@lotdepth=\count299
\sf@top=\skip63
\sf@bottom=\skip64
) (d:/texlive/2024/texmf-dist/tex/latex/base/textcomp.sty
Package: textcomp 2020/02/02 v2.0n Standard LaTeX package
) (d:/texlive/2024/texmf-dist/tex/latex/hyperref/hyperref.sty
Package: hyperref 2024-01-20 v7.01h Hypertext links for LaTeX
 (d:/texlive/2024/texmf-dist/tex/latex/kvsetkeys/kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/kvdefinekeys/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/pdfescape/pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
 (d:/texlive/2024/texmf-dist/tex/generic/ltxcmds/ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/pdftexcmds/pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO)
 (d:/texlive/2024/texmf-dist/tex/generic/infwarerr/infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)) (d:/texlive/2024/texmf-dist/tex/latex/hycolor/hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
) (d:/texlive/2024/texmf-dist/tex/latex/auxhook/auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
) (d:/texlive/2024/texmf-dist/tex/latex/hyperref/nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section
 (d:/texlive/2024/texmf-dist/tex/latex/refcount/refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/gettitlestring/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
 (d:/texlive/2024/texmf-dist/tex/latex/kvoptions/kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count300
)
\@linkdim=\dimen256
\Hy@linkcounter=\count301
\Hy@pagecounter=\count302
 (d:/texlive/2024/texmf-dist/tex/latex/hyperref/pd1enc.def
File: pd1enc.def 2024-01-20 v7.01h Hyperref: PDFDocEncoding definition (HO)
) (d:/texlive/2024/texmf-dist/tex/generic/intcalc/intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count303
 (d:/texlive/2024/texmf-dist/tex/latex/hyperref/puenc.def
File: puenc.def 2024-01-20 v7.01h Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Hyper figures OFF on input line 4179.
Package hyperref Info: Link nesting OFF on input line 4184.
Package hyperref Info: Hyper index ON on input line 4187.
Package hyperref Info: Plain pages OFF on input line 4194.
Package hyperref Info: Backreferencing OFF on input line 4199.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4446.
\c@Hy@tempcnt=\count304
 (d:/texlive/2024/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip17
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4784.
\XeTeXLinkMargin=\dimen257
 (d:/texlive/2024/texmf-dist/tex/generic/bitset/bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)
 (d:/texlive/2024/texmf-dist/tex/generic/bigintcalc/bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO)
))
\Fld@menulength=\count305
\Field@Width=\dimen258
\Fld@charsize=\dimen259
Package hyperref Info: Hyper figures OFF on input line 6063.
Package hyperref Info: Link nesting OFF on input line 6068.
Package hyperref Info: Hyper index ON on input line 6071.
Package hyperref Info: backreferencing OFF on input line 6078.
Package hyperref Info: Link coloring OFF on input line 6083.
Package hyperref Info: Link coloring with OCG OFF on input line 6088.
Package hyperref Info: PDF/A mode OFF on input line 6093.
 (d:/texlive/2024/texmf-dist/tex/latex/base/atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
\Hy@abspage=\count306
\c@Item=\count307
\c@Hfootnote=\count308
)
Package hyperref Info: Driver (autodetected): hxetex.
 (d:/texlive/2024/texmf-dist/tex/latex/hyperref/hxetex.def
File: hxetex.def 2024-01-20 v7.01h Hyperref driver for XeTeX
 (d:/texlive/2024/texmf-dist/tex/generic/stringenc/stringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO)
)
\pdfm@box=\box59
\c@Hy@AnnotLevel=\count309
\HyField@AnnotCount=\count310
\Fld@listcount=\count311
\c@bookmark@seq@number=\count312
 (d:/texlive/2024/texmf-dist/tex/latex/rerunfilecheck/rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)
 (d:/texlive/2024/texmf-dist/tex/latex/base/atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend package
with kernel methods
) (d:/texlive/2024/texmf-dist/tex/generic/uniquecounter/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 285.
)
\Hy@SectionHShift=\skip65
) (d:/texlive/2024/texmf-dist/tex/latex/sttools/stfloats.sty
Package: stfloats 2017/03/27 v3.3 Improve float mechanism and baselineskip settings
\@dblbotnum=\count313
\c@dblbotnumber=\count314
) (d:/texlive/2024/texmf-dist/tex/latex/tools/verbatim.sty
Package: verbatim 2023-11-06 v1.5v LaTeX2e package for verbatim enhancements
\every@verbatim=\toks32
\verbatim@line=\toks33
\verbatim@in@stream=\read3
) (d:/texlive/2024/texmf-dist/tex/latex/cite/cite.sty
LaTeX Info: Redefining \cite on input line 302.
LaTeX Info: Redefining \nocite on input line 332.
Package: cite 2015/02/27  v 5.5
)

./yolo_cea_paper.tex:31: LaTeX Error: Option clash for package hyperref.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.31 \renewcommand
                  {\citeleft}{\textcolor{blue}{[}}
The package hyperref has already been loaded with options:
  []
There has now been an attempt to load it with options
  [colorlinks, linkcolor=blue, anchorcolor=blue, citecolor=blue]
Adding the global options:
  ,colorlinks, linkcolor=blue, anchorcolor=blue, citecolor=blue
to your \documentclass declaration may fix this.
Try typing  <return>  to proceed.

(d:/texlive/2024/texmf-dist/tex/latex/l3backend/l3backend-xetex.def
File: l3backend-xetex.def 2024-02-20 L3 backend support: XeTeX
\g__graphics_track_int=\count315
\l__pdf_internal_box=\box60
\g__pdf_backend_object_int=\count316
\g__pdf_backend_annotation_int=\count317
\g__pdf_backend_link_int=\count318
)

LaTeX Warning: Unused global option(s):
    [lettersize].

No file yolo_cea_paper.aux.
\openout1 = `yolo_cea_paper.aux'.

LaTeX Font Info:    Checking defaults for OML/ntxmi/m/it on input line 37.
LaTeX Font Info:    Trying to load font information for OML+ntxmi on input line 37.
(d:/texlive/2024/texmf-dist/tex/latex/newtx/omlntxmi.fd
File: omlntxmi.fd 2015/08/25 Fontinst v1.933 font definitions for OML/ntxmi.
)
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 37.
LaTeX Font Info:    Trying to load font information for TS1+cmr on input line 37.
 (d:/texlive/2024/texmf-dist/tex/latex/base/ts1cmr.fd
File: ts1cmr.fd 2023/04/13 v2.5m Standard LaTeX font definitions
)
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for U/ntxexa/m/n on input line 37.
LaTeX Font Info:    Trying to load font information for U+ntxexa on input line 37.
 (d:/texlive/2024/texmf-dist/tex/latex/newtx/untxexa.fd
File: untxexa.fd 2012/04/16 Fontinst v1.933 font definitions for U/ntxexa.
)
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for LMS/ntxsy/m/n on input line 37.
LaTeX Font Info:    Trying to load font information for LMS+ntxsy on input line 37.
 (d:/texlive/2024/texmf-dist/tex/latex/newtx/lmsntxsy.fd
File: lmsntxsy.fd 2016/07/02 Fontinst v1.933 font definitions for LMS/ntxsy.
)
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for LMX/ntxexx/m/n on input line 37.
LaTeX Font Info:    Trying to load font information for LMX+ntxexx on input line 37.
 (d:/texlive/2024/texmf-dist/tex/latex/newtx/lmxntxexx.fd
File: lmxntxexx.fd 2016/07/03 Fontinst v1.933 font definitions for LMX/ntxexx.
)
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 37.
LaTeX Font Info:    ... okay on input line 37.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 37.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 37.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 37.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 37.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 37.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 37.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 37.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 37.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 37.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 37.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 37.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 37.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 37.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 37.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 37.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 37.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 37.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 37.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 37.


LaTeX Font Warning: Font shape `TU/ptm/b/it' undefined
(Font)              using `TU/ptm/bx/it' instead on input line 37.

-- Lines per column: 58 (exact).
LaTeX Info: Command `\dddot' is already robust on input line 37.
LaTeX Info: Command `\ddddot' is already robust on input line 37.
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: subfig package v1.3 is loaded.
Package caption Info: End \AtBeginDocument code.
Package hyperref Info: Link coloring OFF on input line 37.
\@outlinefile=\write3
\openout3 = `yolo_cea_paper.out'.


Package hyperref Warning: Rerun to get /PageLabels entry.

[1


]

LaTeX Warning: No \author given.


LaTeX Warning: No \author given.


LaTeX Warning: No \author given.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 56.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 64.


LaTeX Font Warning: Font shape `TU/ptm/m/sc' undefined
(Font)              using `TU/ptm/m/n' instead on input line 68.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 69.

./yolo_cea_paper.tex:69: Undefined control sequence.
\citeleft ->\textcolor 
                       {blue}{[}
l.69 ...fundamental arteries \cite{2022Inspection}
                                                  .
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 69.


LaTeX Warning: Citation `2022Inspection' on page 2 undefined on input line 69.

./yolo_cea_paper.tex:69: Undefined control sequence.
\citeright ->\textcolor 
                        {blue}{]}
l.69 ...fundamental arteries \cite{2022Inspection}
                                                  .
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./yolo_cea_paper.tex:70: Undefined control sequence.
\citeleft ->\textcolor 
                       {blue}{[}
l.70 ...n and mechanical support \cite{2024Region}
                                                  .
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 70.


LaTeX Warning: Citation `2024Region' on page 2 undefined on input line 70.

./yolo_cea_paper.tex:70: Undefined control sequence.
\citeright ->\textcolor 
                        {blue}{]}
l.70 ...n and mechanical support \cite{2024Region}
                                                  .
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./yolo_cea_paper.tex:71: Undefined control sequence.
\citeleft ->\textcolor 
                       {blue}{[}
l.71 ...ge, and cracking \cite{2023Summary,2022An}
                                                  .
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 71.


LaTeX Warning: Citation `2023Summary' on page 2 undefined on input line 71.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 71.


LaTeX Warning: Citation `2022An' on page 2 undefined on input line 71.

./yolo_cea_paper.tex:71: Undefined control sequence.
\citeright ->\textcolor 
                        {blue}{]}
l.71 ...ge, and cracking \cite{2023Summary,2022An}
                                                  .
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./yolo_cea_paper.tex:72: Undefined control sequence.
\citeleft ->\textcolor 
                       {blue}{[}
l.72 ...ignificant economic losses \cite{2021Real}
                                                  . Consequently, the develo...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 72.


LaTeX Warning: Citation `2021Real' on page 2 undefined on input line 72.

./yolo_cea_paper.tex:72: Undefined control sequence.
\citeright ->\textcolor 
                        {blue}{]}
l.72 ...ignificant economic losses \cite{2021Real}
                                                  . Consequently, the develo...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./yolo_cea_paper.tex:74: Undefined control sequence.
\citeleft ->\textcolor 
                       {blue}{[}
l.74 ...n hazardous and inefficient \cite{9400959}
                                                  . The advancement of deep ...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 74.


LaTeX Warning: Citation `9400959' on page 2 undefined on input line 74.

./yolo_cea_paper.tex:74: Undefined control sequence.
\citeright ->\textcolor 
                        {blue}{]}
l.74 ...n hazardous and inefficient \cite{9400959}
                                                  . The advancement of deep ...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./yolo_cea_paper.tex:74: Undefined control sequence.
\citeleft ->\textcolor 
                       {blue}{[}
l.74 ...al imagery \cite{2021InsuDet,2022Improved}
                                                  .
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 74.


LaTeX Warning: Citation `2021InsuDet' on page 2 undefined on input line 74.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 74.


LaTeX Warning: Citation `2022Improved' on page 2 undefined on input line 74.

./yolo_cea_paper.tex:74: Undefined control sequence.
\citeright ->\textcolor 
                        {blue}{]}
l.74 ...al imagery \cite{2021InsuDet,2022Improved}
                                                  .
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./yolo_cea_paper.tex:75: Undefined control sequence.
\citeleft ->\textcolor 
                       {blue}{[}
l.75 ...uracy \cite{2021Improved,coatings13050880}
                                                  .
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 75.


LaTeX Warning: Citation `2021Improved' on page 2 undefined on input line 75.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 75.


LaTeX Warning: Citation `coatings13050880' on page 2 undefined on input line 75.

./yolo_cea_paper.tex:75: Undefined control sequence.
\citeright ->\textcolor 
                        {blue}{]}
l.75 ...uracy \cite{2021Improved,coatings13050880}
                                                  .
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./yolo_cea_paper.tex:77: Undefined control sequence.
\citeleft ->\textcolor 
                       {blue}{[}
l.77 ...insulator,DBLP:journals/eaai/FengYYYLSZ25}
                                                  .
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 77.


LaTeX Warning: Citation `han2022insulator' on page 2 undefined on input line 77.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 77.


LaTeX Warning: Citation `DBLP:journals/eaai/FengYYYLSZ25' on page 2 undefined on input line 77.

./yolo_cea_paper.tex:77: Undefined control sequence.
\citeright ->\textcolor 
                        {blue}{]}
l.77 ...insulator,DBLP:journals/eaai/FengYYYLSZ25}
                                                  .
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.

./yolo_cea_paper.tex:80: Undefined control sequence.
\citeleft ->\textcolor 
                       {blue}{[}
l.80 ...ACS-YOLO presented in \cite{2024CACS-YOLO}
                                                   attempt to create a light...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 80.


LaTeX Warning: Citation `2024CACS-YOLO' on page 2 undefined on input line 80.

./yolo_cea_paper.tex:80: Undefined control sequence.
\citeright ->\textcolor 
                        {blue}{]}
l.80 ...ACS-YOLO presented in \cite{2024CACS-YOLO}
                                                   attempt to create a light...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 83.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 84.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 85.

[2

]

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 87.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 92.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 92.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 93.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 93.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 93.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 94.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 94.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 94.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 98.


LaTeX Warning: Reference `sec:method' on page 3 undefined on input line 98.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 98.


LaTeX Warning: Reference `sec:experimental' on page 3 undefined on input line 98.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 98.


LaTeX Warning: Reference `sec:conclusion' on page 3 undefined on input line 98.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 98.


LaTeX Warning: Reference `sec:conclusion' on page 3 undefined on input line 98.

./yolo_cea_paper.tex:103: Undefined control sequence.
\citeleft ->\textcolor 
                       {blue}{[}
l.103 ...n head components of YOLOv11\cite{yolo11}
                                                  . The overall architecture...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 103.


LaTeX Warning: Citation `yolo11' on page 3 undefined on input line 103.

./yolo_cea_paper.tex:103: Undefined control sequence.
\citeright ->\textcolor 
                        {blue}{]}
l.103 ...n head components of YOLOv11\cite{yolo11}
                                                  . The overall architecture...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 103.


LaTeX Warning: Reference `fig_architecture' on page 3 undefined on input line 103.

File: figures/overfig.pdf Graphic file (type pdf)
<use figures/overfig.pdf>

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 112.


LaTeX Warning: Reference `fig_architecture' on page 3 undefined on input line 112.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 112.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 114.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 116.

[3]

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 121.

./yolo_cea_paper.tex:121: Undefined control sequence.
\citeleft ->\textcolor 
                       {blue}{[}
l.121 ...capabilities of Transformers\cite{2021An}
                                                   with the efficient local ...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 121.


LaTeX Warning: Citation `2021An' on page 4 undefined on input line 121.

./yolo_cea_paper.tex:121: Undefined control sequence.
\citeright ->\textcolor 
                        {blue}{]}
l.121 ...capabilities of Transformers\cite{2021An}
                                                   with the efficient local ...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 123.

LaTeX Font Info:    Trying to load font information for OT1+minntx on input line 123.
(d:/texlive/2024/texmf-dist/tex/latex/newtx/ot1minntx.fd
File: ot1minntx.fd 2023/09/09 v1.1 font definition file for OT1/minntx
)
LaTeX Font Info:    Trying to load font information for U+msa on input line 123.
 (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 123.
 (d:/texlive/2024/texmf-dist/tex/latex/amsfonts/umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Trying to load font information for U+ntxmia on input line 123.
 (d:/texlive/2024/texmf-dist/tex/latex/newtx/untxmia.fd
File: untxmia.fd 2018/04/14 Fontinst v1.933 font definitions for U/ntxmia.
)
LaTeX Font Info:    Trying to load font information for U+ntxsym on input line 123.
 (d:/texlive/2024/texmf-dist/tex/latex/newtx/untxsym.fd
File: untxsym.fd 2023/08/16 Fontinst v1.933 font definitions for U/ntxsym.
)
LaTeX Font Info:    Trying to load font information for U+ntxsyc on input line 123.
 (d:/texlive/2024/texmf-dist/tex/latex/newtx/untxsyc.fd
File: untxsyc.fd 2012/04/12 Fontinst v1.933 font definitions for U/ntxsyc.
)

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 123.


Underfull \hbox (badness 2951) in paragraph at lines 123--124
[]\TU/ptm/m/sc/10 C3k2-HTC replaces the C3k in C3k2 with the
 []


Underfull \hbox (badness 3098) in paragraph at lines 123--124
\TU/ptm/m/sc/10 HTCBlock (Hybrid Transformer Conv Block). The
 []


Underfull \vbox (badness 2042) has occurred while \output is active []

 [4]

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 134.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 139.

./yolo_cea_paper.tex:139: Undefined control sequence.
\citeleft ->\textcolor 
                       {blue}{[}
l.139 ...inear Unit (CGLU)}\cite{shi2024transnext}
                                                  . CGLU uses a dynamic, dat...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 139.


LaTeX Warning: Citation `shi2024transnext' on page 5 undefined on input line 139.

./yolo_cea_paper.tex:139: Undefined control sequence.
\citeright ->\textcolor 
                        {blue}{]}
l.139 ...inear Unit (CGLU)}\cite{shi2024transnext}
                                                  . CGLU uses a dynamic, dat...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 139.

File: figures/HTC.pdf Graphic file (type pdf)
<use figures/HTC.pdf>

LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 154.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 154.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 154.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 154.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 156.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 156.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 156.

./yolo_cea_paper.tex:156: Undefined control sequence.
\citeleft ->\textcolor 
                       {blue}{[}
l.156 ...Enhancement (GSE)}\cite{li2024rethinking}
                                                  . This achieves better fea...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 156.


LaTeX Warning: Citation `li2024rethinking' on page 5 undefined on input line 156.

./yolo_cea_paper.tex:156: Undefined control sequence.
\citeright ->\textcolor 
                        {blue}{]}
l.156 ...Enhancement (GSE)}\cite{li2024rethinking}
                                                  . This achieves better fea...
The control sequence at the end of the top line
of your error message was never \def'ed. If you have
misspelled it (e.g., `\hobx'), type `I' and the correct
spelling (e.g., `I\hbox'). Otherwise just continue,
and I'll forget about whatever was undefined.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 158.


LaTeX Warning: Reference `eq:sni' on page 5 undefined on input line 158.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 166.


LaTeX Font Warning: Font shape `TU/ptm/b/n' undefined
(Font)              using `TU/ptm/bx/n' instead on input line 166.

[5