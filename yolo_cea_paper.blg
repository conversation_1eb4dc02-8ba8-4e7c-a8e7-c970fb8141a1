This is BibTeX, Version 0.99d (TeX Live 2024)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: yolo_cea_paper.aux
The style file: IEEEtran.bst
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Reallocated wiz_functions (elt_size=4) to 6000 items from 3000.
Reallocated singl_function (elt_size=4) to 100 items from 50.
Database file #1: reference.bib
-- IEEEtran.bst version 1.14 (2015/08/26) by <PERSON>.
-- http://www.michaelshell.org/tex/ieeetran/bibtex/
-- See the "IEEEtran_bst_HOWTO.pdf" manual for usage information.

Done.
You've used 45 entries,
            4087 wiz_defined-function locations,
            1055 strings with 15980 characters,
and the built_in function-call counts, 32691 in all, are:
= -- 2574
> -- 937
< -- 213
+ -- 515
- -- 208
* -- 1556
:= -- 4849
add.period$ -- 92
call.type$ -- 45
change.case$ -- 49
chr.to.int$ -- 489
cite$ -- 45
duplicate$ -- 2418
empty$ -- 2641
format.name$ -- 223
if$ -- 7609
int.to.chr$ -- 0
int.to.str$ -- 45
missing$ -- 450
newline$ -- 162
num.names$ -- 45
pop$ -- 1119
preamble$ -- 1
purify$ -- 0
quote$ -- 2
skip$ -- 2511
stack$ -- 0
substring$ -- 1309
swap$ -- 1877
text.length$ -- 50
text.prefix$ -- 0
top$ -- 5
type$ -- 45
warning$ -- 0
while$ -- 118
width$ -- 47
write$ -- 442
