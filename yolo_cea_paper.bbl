% Generated by IEEEtran.bst, version: 1.14 (2015/08/26)
\begin{thebibliography}{10}
\providecommand{\url}[1]{#1}
\csname url@samestyle\endcsname
\providecommand{\newblock}{\relax}
\providecommand{\bibinfo}[2]{#2}
\providecommand{\BIBentrySTDinterwordspacing}{\spaceskip=0pt\relax}
\providecommand{\BIBentryALTinterwordstretchfactor}{4}
\providecommand{\BIBentryALTinterwordspacing}{\spaceskip=\fontdimen2\font plus
\BIBentryALTinterwordstretchfactor\fontdimen3\font minus
  \fontdimen4\font\relax}
\providecommand{\BIBforeignlanguage}[2]{{%
\expandafter\ifx\csname l@#1\endcsname\relax
\typeout{** WARNING: IEEEtran.bst: No hyphenation pattern has been}%
\typeout{** loaded for the language `#1'. Using the pattern for}%
\typeout{** the default language instead.}%
\else
\language=\csname l@#1\endcsname
\fi
#2}}
\providecommand{\BIBdecl}{\relax}
\BIBdecl

\bibitem{2022Inspection}
.~<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, and <PERSON><PERSON>~Sanyal, ``Inspection and identification of
  transmission line insulator breakdown based on deep learning using aerial
  images,'' \emph{Electric Power Systems Research}, 2022.

\bibitem{2024Region}
K.~Qiu, Y.~Cao, D.~Jiang, L.~Chen, and Q.~Yang, ``Region-based active learning
  for insulator defect diagnosis using aerial images of electric transmission
  networks,'' \emph{Power Delivery, IEEE Trans. on (T-PWRD)}, vol.~39, no.~5,
  p.~13, 2024.

\bibitem{2023Summary}
J.~Liu, M.~Hu, J.~Dong, and X.~Lu, ``Summary of insulator defect detection
  based on deep learning,'' \emph{Electric Power Systems Research}, 2023.

\bibitem{2022An}
H.~Xia, B.~Yang, Y.~Li, and B.~Wang, ``An improved centernet model for
  insulator defect detection using aerial imagery,'' \emph{Sensors (14248220)},
  vol.~22, no.~8, 2022.

\bibitem{2021Real}
Y.~Ma, Q.~Li, L.~Chu, Y.~Zhou, and C.~Xu, ``Real-time detection and spatial
  localization of insulators for uav inspection based on binocular stereo
  vision,'' \emph{Remote Sensing}, vol.~13, no.~2, p. 230, 2021.

\bibitem{9400959}
A.~El-Hag, ``Application of machine learning in outdoor insulators condition
  monitoring and diagnostics,'' \emph{IEEE Instrumentation \& Measurement
  Magazine}, vol.~24, no.~2, pp. 101--108, 2021.

\bibitem{2021InsuDet}
X.~Zhang, Y.~Zhang, J.~Liu, C.~Zhang, X.~Xue, H.~Zhang, and W.~Zhang,
  ``Insudet: A fault detection method for insulators of overhead transmission
  lines using convolutional neural networks,'' \emph{IEEE Transactions on
  Instrumentation and Measurement}, no. 70-, 2021.

\bibitem{2022Improved}
L.~Qin, ``Improved algorithm for insulator and its defect detection based on
  yolox,'' \emph{Sensors}, vol.~22, 2022.

\bibitem{2021Improved}
C.~Liu, Y.~Wu, J.~Liu, and Z.~Sun, ``Improved yolov3 network for insulator
  detection in aerial images with diverse background interference,''
  \emph{Electronics}, vol.~10, no.~7, p. 771, 2021.

\bibitem{coatings13050880}
\BIBentryALTinterwordspacing
R.~Chang, S.~Zhou, Y.~Zhang, N.~Zhang, C.~Zhou, and M.~Li, ``Research on
  insulator defect detection based on improved yolov7 and multi-uav cooperative
  system,'' \emph{Coatings}, vol.~13, no.~5, 2023. [Online]. Available:
  \url{https://www.mdpi.com/2079-6412/13/5/880}
\BIBentrySTDinterwordspacing

\bibitem{han2022insulator}
G.~Han, M.~He, M.~Gao, J.~Yu, K.~Liu, and L.~Qin, ``Insulator breakage
  detection based on improved yolov5,'' \emph{Sustainability}, vol.~14, no.~10,
  p. 6066, 2022.

\bibitem{DBLP:journals/eaai/FengYYYLSZ25}
\BIBentryALTinterwordspacing
F.~Feng, X.~Yang, R.~Yang, H.~Yu, F.~Liao, Q.~Shi, and F.~Zhu, ``An insulator
  defect detection network combining bidirectional feature pyramid network and
  attention mechanism in unmanned aerial vehicle images,'' \emph{Eng. Appl.
  Artif. Intell.}, vol. 152, p. 110745, 2025. [Online]. Available:
  \url{https://doi.org/10.1016/j.engappai.2025.110745}
\BIBentrySTDinterwordspacing

\bibitem{2024CACS-YOLO}
Z.~Cao, K.~Chen, J.~Chen, Z.~Chen, and M.~Zhang, ``Cacs-yolo: A lightweight
  model for insulator defect detection based on improved yolov8m,'' \emph{IEEE
  Transactions on Instrumentation and Measurement}, p.~73, 2024.

\bibitem{yolo11}
G.~Jocher and J.~Qiu, ``{Ultralytics YOLO11},''
  \url{https://github.com/ultralytics/ultralytics}, 2024, version 11.0.0.

\bibitem{2021An}
A.~Dosovitskiy, L.~Beyer, A.~Kolesnikov, D.~Weissenborn, X.~Zhai,
  T.~Unterthiner, M.~Dehghani, M.~Minderer, G.~Heigold, and S.~Gelly, ``An
  image is worth 16x16 words: Transformers for image recognition at scale,'' in
  \emph{International Conference on Learning Representations}, 2021.

\bibitem{shi2024transnext}
D.~Shi, ``Transnext: Robust foveal visual perception for vision transformers,''
  in \emph{Proceedings of the IEEE/CVF conference on computer vision and
  pattern recognition}, 2024, pp. 17\,773--17\,783.

\bibitem{li2024rethinking}
H.~Li, ``Rethinking features-fused-pyramid-neck for object detection,'' in
  \emph{European Conference on Computer Vision}.\hskip 1em plus 0.5em minus
  0.4em\relax Springer, 2024, pp. 74--90.

\bibitem{chollet2017xception}
F.~Chollet, ``Xception: Deep learning with depthwise separable convolutions,''
  in \emph{Proceedings of the IEEE conference on computer vision and pattern
  recognition}, 2017, pp. 1251--1258.

\bibitem{hendrycks2016gaussian}
D.~Hendrycks and K.~Gimpel, ``Gaussian error linear units (gelus),''
  \emph{arXiv preprint arXiv:1606.08415}, 2016.

\bibitem{zhang2018shufflenet}
X.~Zhang, X.~Zhou, M.~Lin, and J.~Sun, ``Shufflenet: An extremely efficient
  convolutional neural network for mobile devices,'' in \emph{Proceedings of
  the IEEE conference on computer vision and pattern recognition}, 2018, pp.
  6848--6856.

\bibitem{tian2019fcos}
Z.~Tian, C.~Shen, H.~Chen, and T.~He, ``Fcos: Fully convolutional one-stage
  object detection,'' in \emph{Proceedings of the IEEE/CVF international
  conference on computer vision}, 2019, pp. 9627--9636.

\bibitem{li2021generalized}
X.~Li, W.~Wang, X.~Hu, J.~Li, J.~Tang, and J.~Yang, ``Generalized focal loss
  v2: Learning reliable localization quality estimation for dense object
  detection,'' in \emph{Proceedings of the IEEE/CVF conference on computer
  vision and pattern recognition}, 2021, pp. 11\,632--11\,641.

\bibitem{tao2018detection}
X.~Tao, D.~Zhang, Z.~Wang, X.~Liu, H.~Zhang, and D.~Xu, ``Detection of power
  line insulator defects using aerial images analyzed with convolutional neural
  networks,'' \emph{IEEE Transactions on Systems, Man, and Cybernetics:
  Systems}, 2018.

\bibitem{vkdw-x769-21}
D.~Lewis and P.~Kulkarni, ``{Insulator Defect Detection},''
  \url{https://dx.doi.org/10.21227/vkdw-x769}, 2021, dOI: 10.21227/vkdw-x769.

\bibitem{liu2024yolo}
B.~Liu and W.~Jiang, ``La-yolo: Bidirectional adaptive feature fusion approach
  for small object detection of insulator self-explosion defects,'' \emph{IEEE
  Transactions on Power Delivery}, 2024.

\bibitem{jin2025real}
L.~Jin, W.~Ding, S.~Han, and J.~Wang, ``A real-time edge inference method for
  insulator contamination detection with yolov11-ssl,'' \emph{IEEE Transactions
  on Instrumentation and Measurement}, 2025.

\bibitem{10471592}
G.~Yang, J.~Lei, H.~Tian, Z.~Feng, and R.~Liang, ``Asymptotic feature pyramid
  network for labeling pixels and regions,'' \emph{IEEE Transactions on
  Circuits and Systems for Video Technology}, vol.~34, no.~9, pp. 7820--7829,
  2024.

\bibitem{tan2020efficientdet}
M.~Tan, R.~Pang, and Q.~V. Le, ``Efficientdet: Scalable and efficient object
  detection,'' in \emph{Proceedings of the IEEE/CVF conference on computer
  vision and pattern recognition}, 2020, pp. 10\,781--10\,790.

\bibitem{wu2025crl}
D.~Wu, W.~Yang, J.~Li, K.~Du, L.~Li, and Z.~Yang, ``Crl-yolo: A comprehensive
  recalibration and lightweight detection model for uav power line
  inspections,'' \emph{IEEE Transactions on Instrumentation and Measurement},
  2025.

\bibitem{ren2016faster}
S.~Ren, K.~He, R.~Girshick, and J.~Sun, ``Faster r-cnn: Towards real-time
  object detection with region proposal networks,'' \emph{IEEE transactions on
  pattern analysis and machine intelligence}, vol.~39, no.~6, pp. 1137--1149,
  2016.

\bibitem{cai2018cascade}
Z.~Cai and N.~Vasconcelos, ``Cascade r-cnn: Delving into high quality object
  detection,'' in \emph{Proceedings of the IEEE conference on computer vision
  and pattern recognition}, 2018, pp. 6154--6162.

\bibitem{zhang2020dynamic}
H.~Zhang, H.~Chang, B.~Ma, N.~Wang, and X.~Chen, ``Dynamic r-cnn: Towards high
  quality object detection via dynamic training,'' in \emph{European conference
  on computer vision}.\hskip 1em plus 0.5em minus 0.4em\relax Springer, 2020,
  pp. 260--275.

\bibitem{pang2019libra}
J.~Pang, K.~Chen, J.~Shi, H.~Feng, W.~Ouyang, and D.~Lin, ``Libra r-cnn:
  Towards balanced learning for object detection,'' in \emph{Proceedings of the
  IEEE/CVF conference on computer vision and pattern recognition}, 2019, pp.
  821--830.

\bibitem{lin2017focal}
T.-Y. Lin, P.~Goyal, R.~Girshick, K.~He, and P.~Doll{\'a}r, ``Focal loss for
  dense object detection,'' in \emph{Proceedings of the IEEE international
  conference on computer vision}, 2017, pp. 2980--2988.

\bibitem{yolov3}
J.~Redmon and A.~Farhadi, ``{YOLOv3: An incremental improvement},'' \emph{arXiv
  preprint arXiv:1804.02767}, 2018.

\bibitem{yolov5}
G.~Jocher, ``{Ultralytics YOLOv5},''
  \url{https://github.com/ultralytics/yolov5}, 2020, version 7.0, DOI:
  10.5281/zenodo.3908559.

\bibitem{yolov6}
C.~Li, L.~Li, Y.~Geng, H.~Jiang, M.~Cheng, B.~Zhang, Z.~Ke, X.~Xu, and X.~Chu,
  ``{YOLOv6 v3.0: A full-scale reloading},'' \emph{arXiv preprint
  arXiv:2301.05586}, 2023.

\bibitem{yolov7}
C.-Y. Wang, A.~Bochkovskiy, and H.-Y.~M. Liao, ``{YOLOv7: Trainable
  bag-of-freebies sets new state-of-the-art for real-time object detectors},''
  \emph{arXiv preprint arXiv:2207.02696}, 2022.

\bibitem{yolov8}
G.~Jocher, A.~Chaurasia, and J.~Qiu, ``{Ultralytics YOLOv8},''
  \url{https://github.com/ultralytics/ultralytics}, 2023, version 8.0.0.

\bibitem{yolov9}
C.-Y. Wang and H.-Y.~M. Liao, ``{YOLOv9: Learning what you want to learn using
  programmable gradient information},'' \emph{arXiv preprint arXiv:2402.13616},
  2024.

\bibitem{yolov10}
A.~Wang, H.~Chen, L.~Liu, K.~Chen, Z.~Lin, J.~Han \emph{et~al.}, ``Yolov10:
  Real-time end-to-end object detection,'' \emph{Advances in Neural Information
  Processing Systems}, vol.~37, pp. 107\,984--108\,011, 2024.

\bibitem{yolov12}
Y.~Tian, Q.~Ye, and D.~Doermann, ``{YOLOv12: Attention-centric real-time object
  detectors},'' \emph{arXiv preprint arXiv:2502.12524}, 2025.

\bibitem{wang2024mci}
Y.~Wang, X.~Song, L.~Feng, Y.~Zhai, Z.~Zhao, S.~Zhang, and Q.~Wang, ``Mci-gla
  plug-in suitable for yolo series models for transmission line insulator
  defect detection,'' \emph{IEEE Transactions on Instrumentation and
  Measurement}, vol.~73, pp. 1--12, 2024.

\bibitem{tao2024snakenet}
Z.~Tao, Y.~He, S.~Lin, T.~Yi, and M.~Li, ``Snakenet: An adaptive network for
  small object and complex background for insulator surface defect detection,''
  \emph{Computers and Electrical Engineering}, vol. 117, p. 109259, 2024.

\bibitem{pradeep2025improved}
V.~Pradeep, K.~Baskaran, and S.~I. Evangeline, ``An improved transfer learning
  model for detection of insulator defects in power transmission lines,''
  \emph{Neural Computing and Applications}, vol.~37, no.~9, pp. 6951--6976,
  2025.

\end{thebibliography}
